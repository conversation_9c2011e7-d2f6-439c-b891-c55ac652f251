// qcalendarwidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCalendarWidget : public QWidget
{
%TypeHeaderCode
#include <qcalendarwidget.h>
%End

public:
    enum HorizontalHeaderFormat
    {
        NoHorizontalHeader,
        SingleLetterDayNames,
        ShortDayNames,
        LongDayNames,
    };

    enum VerticalHeaderFormat
    {
        NoVerticalHeader,
        ISOWeekNumbers,
    };

    enum SelectionMode
    {
        NoSelection,
        SingleSelection,
    };

    explicit QCalendarWidget(QWidget *parent /TransferThis/ = 0);
    virtual ~QCalendarWidget();
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    QDate selectedDate() const;
    int yearShown() const;
    int monthShown() const;
    QDate minimumDate() const;
    void setMinimumDate(const QDate &date);
    QDate maximumDate() const;
    void setMaximumDate(const QDate &date);
    Qt::DayOfWeek firstDayOfWeek() const;
    void setFirstDayOfWeek(Qt::DayOfWeek dayOfWeek);
    bool isGridVisible() const;
    void setGridVisible(bool show);
    QCalendarWidget::SelectionMode selectionMode() const;
    void setSelectionMode(QCalendarWidget::SelectionMode mode);
    QCalendarWidget::HorizontalHeaderFormat horizontalHeaderFormat() const;
    void setHorizontalHeaderFormat(QCalendarWidget::HorizontalHeaderFormat format);
    QCalendarWidget::VerticalHeaderFormat verticalHeaderFormat() const;
    void setVerticalHeaderFormat(QCalendarWidget::VerticalHeaderFormat format);
    QTextCharFormat headerTextFormat() const;
    void setHeaderTextFormat(const QTextCharFormat &format);
    QTextCharFormat weekdayTextFormat(Qt::DayOfWeek dayOfWeek) const;
    void setWeekdayTextFormat(Qt::DayOfWeek dayOfWeek, const QTextCharFormat &format);
    QMap<QDate, QTextCharFormat> dateTextFormat() const;
    QTextCharFormat dateTextFormat(const QDate &date) const;
    void setDateTextFormat(const QDate &date, const QTextCharFormat &color);

protected:
    void updateCell(const QDate &date);
    void updateCells();
    virtual bool event(QEvent *event);
    virtual bool eventFilter(QObject *watched, QEvent *event);
    virtual void mousePressEvent(QMouseEvent *event);
    virtual void resizeEvent(QResizeEvent *event);
    virtual void keyPressEvent(QKeyEvent *event);
    virtual void paintCell(QPainter *painter, const QRect &rect, const QDate &date) const;

public slots:
    void setCurrentPage(int year, int month);
    void setDateRange(const QDate &min, const QDate &max);
    void setSelectedDate(const QDate &date);
    void showNextMonth();
    void showNextYear();
    void showPreviousMonth();
    void showPreviousYear();
    void showSelectedDate();
    void showToday();

signals:
    void activated(const QDate &date);
    void clicked(const QDate &date);
    void currentPageChanged(int year, int month);
    void selectionChanged();

public:
    bool isNavigationBarVisible() const;
    bool isDateEditEnabled() const;
    void setDateEditEnabled(bool enable);
    int dateEditAcceptDelay() const;
    void setDateEditAcceptDelay(int delay);

public slots:
    void setNavigationBarVisible(bool visible);

public:
%If (Qt_5_14_0 -)
    QCalendar calendar() const;
%End
%If (Qt_5_14_0 -)
    void setCalendar(QCalendar calendar);
%End
};
