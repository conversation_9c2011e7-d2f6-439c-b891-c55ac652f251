// qsslerror.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_SSL)

class QSslError
{
%TypeHeaderCode
#include <qsslerror.h>
%End

public:
    enum SslError
    {
        UnspecifiedError,
        NoError,
        UnableToGetIssuerCertificate,
        UnableToDecryptCertificateSignature,
        UnableToDecodeIssuerPublicKey,
        CertificateSignatureFailed,
        CertificateNotYetValid,
        CertificateExpired,
        InvalidNotBeforeField,
        InvalidNotAfterField,
        SelfSignedCertificate,
        SelfSignedCertificateInChain,
        UnableToGetLocalIssuerCertificate,
        UnableToVerifyFirstCertificate,
        CertificateRevoked,
        InvalidCaCertificate,
        PathLengthExceeded,
        InvalidPurpose,
        CertificateUntrusted,
        CertificateRejected,
        SubjectIssuerMismatch,
        AuthorityIssuerSerialNumberMismatch,
        NoPeerCertificate,
        HostNameMismatch,
        NoSslSupport,
        CertificateBlacklisted,
%If (Qt_5_13_0 -)
        CertificateStatusUnknown,
%End
%If (Qt_5_13_0 -)
        OcspNoResponseFound,
%End
%If (Qt_5_13_0 -)
        OcspMalformedRequest,
%End
%If (Qt_5_13_0 -)
        OcspMalformedResponse,
%End
%If (Qt_5_13_0 -)
        OcspInternalError,
%End
%If (Qt_5_13_0 -)
        OcspTryLater,
%End
%If (Qt_5_13_0 -)
        OcspSigRequred,
%End
%If (Qt_5_13_0 -)
        OcspUnauthorized,
%End
%If (Qt_5_13_0 -)
        OcspResponseCannotBeTrusted,
%End
%If (Qt_5_13_0 -)
        OcspResponseCertIdUnknown,
%End
%If (Qt_5_13_0 -)
        OcspResponseExpired,
%End
%If (Qt_5_13_0 -)
        OcspStatusUnknown,
%End
    };

    QSslError();
    QSslError(QSslError::SslError error);
    QSslError(QSslError::SslError error, const QSslCertificate &certificate);
    QSslError(const QSslError &other);
    ~QSslError();
    QSslError::SslError error() const;
    QString errorString() const;
    QSslCertificate certificate() const;
    bool operator==(const QSslError &other) const;
    bool operator!=(const QSslError &other) const;
    void swap(QSslError &other /Constrained/);
%If (Qt_5_4_0 -)
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
};

%End
