// QtQuickmod.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt5.QtQuick, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtGui/QtGuimod.sip
%Import QtQml/QtQmlmod.sip

%Copying
Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt5.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype sip.simplewrapper

%Include qquickframebufferobject.sip
%Include qquickimageprovider.sip
%Include qquickitem.sip
%Include qquickitemgrabresult.sip
%Include qquickpainteditem.sip
%Include qquickrendercontrol.sip
%Include qquicktextdocument.sip
%Include qquickview.sip
%Include qquickwindow.sip
%Include qsgabstractrenderer.sip
%Include qsgengine.sip
%Include qsgflatcolormaterial.sip
%Include qsggeometry.sip
%Include qsgimagenode.sip
%Include qsgmaterial.sip
%Include qsgmaterialrhishader.sip
%Include qsgnode.sip
%Include qsgrectanglenode.sip
%Include qsgrendererinterface.sip
%Include qsgrendernode.sip
%Include qsgsimplerectnode.sip
%Include qsgsimpletexturenode.sip
%Include qsgtexture.sip
%Include qsgtexturematerial.sip
%Include qsgtextureprovider.sip
%Include qsgvertexcolormaterial.sip
