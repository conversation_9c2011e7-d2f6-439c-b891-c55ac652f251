// qsystemtrayicon.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSystemTrayIcon : public QObject
{
%TypeHeaderCode
#include <qsystemtrayicon.h>
%End

public:
    enum ActivationReason
    {
        Unknown,
        Context,
        DoubleClick,
        Trigger,
        MiddleClick,
    };

    enum MessageIcon
    {
        NoIcon,
        Information,
        Warning,
        Critical,
    };

    QSystemTrayIcon(QObject *parent /TransferThis/ = 0);
    QSystemTrayIcon(const QIcon &icon, QObject *parent /TransferThis/ = 0);
    virtual ~QSystemTrayIcon();
    void setContextMenu(QMenu *menu /KeepReference/);
    QMenu *contextMenu() const;
    QRect geometry() const;
    QIcon icon() const;
    void setIcon(const QIcon &icon);
    QString toolTip() const;
    void setToolTip(const QString &tip);
    static bool isSystemTrayAvailable();
    static bool supportsMessages();

public slots:
    void showMessage(const QString &title, const QString &msg, QSystemTrayIcon::MessageIcon icon = QSystemTrayIcon::Information, int msecs = 10000);
%If (Qt_5_9_0 -)
    void showMessage(const QString &title, const QString &msg, const QIcon &icon, int msecs = 10000);
%End

public:
    bool isVisible() const;

public slots:
    void hide();
    void setVisible(bool visible);
    void show();

signals:
    void activated(QSystemTrayIcon::ActivationReason reason);
    void messageClicked();

protected:
    virtual bool event(QEvent *event);
};
