// qstyleoption.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type)
    {
    case QStyleOption::SO_Button:
        sipType = sipType_QStyleOptionButton;
        break;
    
    case QStyleOption::SO_ComboBox:
        sipType = sipType_QStyleOptionComboBox;
        break;
    
    case QStyleOption::SO_DockWidget:
        sipType = sipType_QStyleOptionDockWidget;
        break;
    
    case QStyleOption::SO_FocusRect:
        sipType = sipType_QStyleOptionFocusRect;
        break;
    
    case QStyleOption::SO_Frame:
        sipType = sipType_QStyleOptionFrame;
        break;
    
    case QStyleOption::SO_GraphicsItem:
        sipType = sipType_QStyleOptionGraphicsItem;
        break;
    
    case QStyleOption::SO_GroupBox:
        sipType = sipType_QStyleOptionGroupBox;
        break;
    
    case QStyleOption::SO_Header:
        sipType = sipType_QStyleOptionHeader;
        break;
    
    case QStyleOption::SO_MenuItem:
        sipType = sipType_QStyleOptionMenuItem;
        break;
    
    case QStyleOption::SO_ProgressBar:
        sipType = sipType_QStyleOptionProgressBar;
        break;
    
    case QStyleOption::SO_RubberBand:
        sipType = sipType_QStyleOptionRubberBand;
        break;
    
    case QStyleOption::SO_SizeGrip:
        sipType = sipType_QStyleOptionSizeGrip;
        break;
    
    case QStyleOption::SO_Slider:
        sipType = sipType_QStyleOptionSlider;
        break;
    
    case QStyleOption::SO_SpinBox:
        sipType = sipType_QStyleOptionSpinBox;
        break;
    
    case QStyleOption::SO_Tab:
        sipType = sipType_QStyleOptionTab;
        break;
    
    case QStyleOption::SO_TabBarBase:
        sipType = sipType_QStyleOptionTabBarBase;
        break;
    
    case QStyleOption::SO_TabWidgetFrame:
        sipType = sipType_QStyleOptionTabWidgetFrame;
        break;
    
    case QStyleOption::SO_TitleBar:
        sipType = sipType_QStyleOptionTitleBar;
        break;
    
    case QStyleOption::SO_ToolBar:
        sipType = sipType_QStyleOptionToolBar;
        break;
    
    case QStyleOption::SO_ToolBox:
        sipType = sipType_QStyleOptionToolBox;
        break;
    
    case QStyleOption::SO_ToolButton:
        sipType = sipType_QStyleOptionToolButton;
        break;
    
    case QStyleOption::SO_ViewItem:
        sipType = sipType_QStyleOptionViewItem;
        break;
    
    default:
        if ((sipCpp->type & QStyleOption::SO_ComplexCustomBase) == QStyleOption::SO_ComplexCustomBase)
            sipType = sipType_QStyleOptionComplex;
        else
            sipType = 0;
    }
%End

public:
    enum OptionType
    {
        SO_Default,
        SO_FocusRect,
        SO_Button,
        SO_Tab,
        SO_MenuItem,
        SO_Frame,
        SO_ProgressBar,
        SO_ToolBox,
        SO_Header,
        SO_DockWidget,
        SO_ViewItem,
        SO_TabWidgetFrame,
        SO_TabBarBase,
        SO_RubberBand,
        SO_ToolBar,
        SO_Complex,
        SO_Slider,
        SO_SpinBox,
        SO_ToolButton,
        SO_ComboBox,
        SO_TitleBar,
        SO_GroupBox,
        SO_ComplexCustomBase,
        SO_GraphicsItem,
        SO_SizeGrip,
        SO_CustomBase,
    };

    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    int version;
    int type;
    QStyle::State state;
    Qt::LayoutDirection direction;
    QRect rect;
    QFontMetrics fontMetrics;
    QPalette palette;
    QObject *styleObject;
    QStyleOption(int version = QStyleOption::StyleOptionVersion::Version, int type = QStyleOption::OptionType::SO_Default);
    QStyleOption(const QStyleOption &other);
    ~QStyleOption();
    void initFrom(const QWidget *w);
};

class QStyleOptionFocusRect : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QColor backgroundColor;
    QStyleOptionFocusRect();
    QStyleOptionFocusRect(const QStyleOptionFocusRect &other);
};

class QStyleOptionFrame : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    enum FrameFeature
    {
        None /PyName=None_/,
        Flat,
        Rounded,
    };

    typedef QFlags<QStyleOptionFrame::FrameFeature> FrameFeatures;
    QStyleOptionFrame::FrameFeatures features;
    QFrame::Shape frameShape;
    int lineWidth;
    int midLineWidth;
    QStyleOptionFrame();
    QStyleOptionFrame(const QStyleOptionFrame &other);
};

QFlags<QStyleOptionFrame::FrameFeature> operator|(QStyleOptionFrame::FrameFeature f1, QFlags<QStyleOptionFrame::FrameFeature> f2);

class QStyleOptionTabWidgetFrame : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    int lineWidth;
    int midLineWidth;
    QTabBar::Shape shape;
    QSize tabBarSize;
    QSize rightCornerWidgetSize;
    QSize leftCornerWidgetSize;
    QRect tabBarRect;
    QRect selectedTabRect;
    QStyleOptionTabWidgetFrame();
    QStyleOptionTabWidgetFrame(const QStyleOptionTabWidgetFrame &other);
};

class QStyleOptionTabBarBase : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QTabBar::Shape shape;
    QRect tabBarRect;
    QRect selectedTabRect;
    bool documentMode;
    QStyleOptionTabBarBase();
    QStyleOptionTabBarBase(const QStyleOptionTabBarBase &other);
};

class QStyleOptionHeader : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    enum SectionPosition
    {
        Beginning,
        Middle,
        End,
        OnlyOneSection,
    };

    enum SelectedPosition
    {
        NotAdjacent,
        NextIsSelected,
        PreviousIsSelected,
        NextAndPreviousAreSelected,
    };

    enum SortIndicator
    {
        None /PyName=None_/,
        SortUp,
        SortDown,
    };

    int section;
    QString text;
    Qt::Alignment textAlignment;
    QIcon icon;
    Qt::Alignment iconAlignment;
    QStyleOptionHeader::SectionPosition position;
    QStyleOptionHeader::SelectedPosition selectedPosition;
    QStyleOptionHeader::SortIndicator sortIndicator;
    Qt::Orientation orientation;
    QStyleOptionHeader();
    QStyleOptionHeader(const QStyleOptionHeader &other);
};

class QStyleOptionButton : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    enum ButtonFeature
    {
        None /PyName=None_/,
        Flat,
        HasMenu,
        DefaultButton,
        AutoDefaultButton,
        CommandLinkButton,
    };

    typedef QFlags<QStyleOptionButton::ButtonFeature> ButtonFeatures;
    QStyleOptionButton::ButtonFeatures features;
    QString text;
    QIcon icon;
    QSize iconSize;
    QStyleOptionButton();
    QStyleOptionButton(const QStyleOptionButton &other);
};

QFlags<QStyleOptionButton::ButtonFeature> operator|(QStyleOptionButton::ButtonFeature f1, QFlags<QStyleOptionButton::ButtonFeature> f2);

class QStyleOptionTab : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    enum TabPosition
    {
        Beginning,
        Middle,
        End,
        OnlyOneTab,
    };

    enum SelectedPosition
    {
        NotAdjacent,
        NextIsSelected,
        PreviousIsSelected,
    };

    enum CornerWidget
    {
        NoCornerWidgets,
        LeftCornerWidget,
        RightCornerWidget,
    };

    typedef QFlags<QStyleOptionTab::CornerWidget> CornerWidgets;
    QTabBar::Shape shape;
    QString text;
    QIcon icon;
    int row;
    QStyleOptionTab::TabPosition position;
    QStyleOptionTab::SelectedPosition selectedPosition;
    QStyleOptionTab::CornerWidgets cornerWidgets;
    QSize iconSize;
    bool documentMode;
    QSize leftButtonSize;
    QSize rightButtonSize;

    enum TabFeature
    {
        None /PyName=None_/,
        HasFrame,
    };

    typedef QFlags<QStyleOptionTab::TabFeature> TabFeatures;
    QStyleOptionTab::TabFeatures features;
    QStyleOptionTab();
    QStyleOptionTab(const QStyleOptionTab &other);
};

QFlags<QStyleOptionTab::CornerWidget> operator|(QStyleOptionTab::CornerWidget f1, QFlags<QStyleOptionTab::CornerWidget> f2);
%If (Qt_5_15_0 -)

class QStyleOptionTabV4 : public QStyleOptionTab
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionVersion
    {
        Version,
    };

    QStyleOptionTabV4();
    int tabIndex;
};

%End

class QStyleOptionProgressBar : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    int minimum;
    int maximum;
    int progress;
    QString text;
    Qt::Alignment textAlignment;
    bool textVisible;
    Qt::Orientation orientation;
    bool invertedAppearance;
    bool bottomToTop;
    QStyleOptionProgressBar();
    QStyleOptionProgressBar(const QStyleOptionProgressBar &other);
};

class QStyleOptionMenuItem : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    enum MenuItemType
    {
        Normal,
        DefaultItem,
        Separator,
        SubMenu,
        Scroller,
        TearOff,
        Margin,
        EmptyArea,
    };

    enum CheckType
    {
        NotCheckable,
        Exclusive,
        NonExclusive,
    };

    QStyleOptionMenuItem::MenuItemType menuItemType;
    QStyleOptionMenuItem::CheckType checkType;
    bool checked;
    bool menuHasCheckableItems;
    QRect menuRect;
    QString text;
    QIcon icon;
    int maxIconWidth;
    int tabWidth;
    QFont font;
    QStyleOptionMenuItem();
    QStyleOptionMenuItem(const QStyleOptionMenuItem &other);
};

class QStyleOptionDockWidget : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QString title;
    bool closable;
    bool movable;
    bool floatable;
    bool verticalTitleBar;
    QStyleOptionDockWidget();
    QStyleOptionDockWidget(const QStyleOptionDockWidget &other);
};

class QStyleOptionViewItem : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    enum Position
    {
        Left,
        Right,
        Top,
        Bottom,
    };

    Qt::Alignment displayAlignment;
    Qt::Alignment decorationAlignment;
    Qt::TextElideMode textElideMode;
    QStyleOptionViewItem::Position decorationPosition;
    QSize decorationSize;
    QFont font;
    bool showDecorationSelected;

    enum ViewItemFeature
    {
        None /PyName=None_/,
        WrapText,
        Alternate,
        HasCheckIndicator,
        HasDisplay,
        HasDecoration,
    };

    typedef QFlags<QStyleOptionViewItem::ViewItemFeature> ViewItemFeatures;
    QStyleOptionViewItem::ViewItemFeatures features;
    QLocale locale;
    const QWidget *widget;

    enum ViewItemPosition
    {
        Invalid,
        Beginning,
        Middle,
        End,
        OnlyOne,
    };

    QModelIndex index;
    Qt::CheckState checkState;
    QIcon icon;
    QString text;
    QStyleOptionViewItem::ViewItemPosition viewItemPosition;
    QBrush backgroundBrush;
    QStyleOptionViewItem();
    QStyleOptionViewItem(const QStyleOptionViewItem &other);
};

QFlags<QStyleOptionViewItem::ViewItemFeature> operator|(QStyleOptionViewItem::ViewItemFeature f1, QFlags<QStyleOptionViewItem::ViewItemFeature> f2);

class QStyleOptionToolBox : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QString text;
    QIcon icon;

    enum TabPosition
    {
        Beginning,
        Middle,
        End,
        OnlyOneTab,
    };

    enum SelectedPosition
    {
        NotAdjacent,
        NextIsSelected,
        PreviousIsSelected,
    };

    QStyleOptionToolBox::TabPosition position;
    QStyleOptionToolBox::SelectedPosition selectedPosition;
    QStyleOptionToolBox();
    QStyleOptionToolBox(const QStyleOptionToolBox &other);
};

class QStyleOptionRubberBand : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QRubberBand::Shape shape;
    bool opaque;
    QStyleOptionRubberBand();
    QStyleOptionRubberBand(const QStyleOptionRubberBand &other);
};

class QStyleOptionComplex : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QStyle::SubControls subControls;
    QStyle::SubControls activeSubControls;
    QStyleOptionComplex(int version = QStyleOptionComplex::StyleOptionVersion::Version, int type = QStyleOption::OptionType::SO_Complex);
    QStyleOptionComplex(const QStyleOptionComplex &other);
};

class QStyleOptionSlider : public QStyleOptionComplex
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    Qt::Orientation orientation;
    int minimum;
    int maximum;
    QSlider::TickPosition tickPosition;
    int tickInterval;
    bool upsideDown;
    int sliderPosition;
    int sliderValue;
    int singleStep;
    int pageStep;
    qreal notchTarget;
    bool dialWrapping;
    QStyleOptionSlider();
    QStyleOptionSlider(const QStyleOptionSlider &other);
};

class QStyleOptionSpinBox : public QStyleOptionComplex
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QAbstractSpinBox::ButtonSymbols buttonSymbols;
    QAbstractSpinBox::StepEnabled stepEnabled;
    bool frame;
    QStyleOptionSpinBox();
    QStyleOptionSpinBox(const QStyleOptionSpinBox &other);
};

class QStyleOptionToolButton : public QStyleOptionComplex
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    enum ToolButtonFeature
    {
        None /PyName=None_/,
        Arrow,
        Menu,
        PopupDelay,
        MenuButtonPopup,
        HasMenu,
    };

    typedef QFlags<QStyleOptionToolButton::ToolButtonFeature> ToolButtonFeatures;
    QStyleOptionToolButton::ToolButtonFeatures features;
    QIcon icon;
    QSize iconSize;
    QString text;
    Qt::ArrowType arrowType;
    Qt::ToolButtonStyle toolButtonStyle;
    QPoint pos;
    QFont font;
    QStyleOptionToolButton();
    QStyleOptionToolButton(const QStyleOptionToolButton &other);
};

QFlags<QStyleOptionToolButton::ToolButtonFeature> operator|(QStyleOptionToolButton::ToolButtonFeature f1, QFlags<QStyleOptionToolButton::ToolButtonFeature> f2);

class QStyleOptionComboBox : public QStyleOptionComplex
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    bool editable;
    QRect popupRect;
    bool frame;
    QString currentText;
    QIcon currentIcon;
    QSize iconSize;
    QStyleOptionComboBox();
    QStyleOptionComboBox(const QStyleOptionComboBox &other);
};

class QStyleOptionTitleBar : public QStyleOptionComplex
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QString text;
    QIcon icon;
    int titleBarState;
    Qt::WindowFlags titleBarFlags;
    QStyleOptionTitleBar();
    QStyleOptionTitleBar(const QStyleOptionTitleBar &other);
};

class QStyleHintReturn
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum HintReturnType
    {
        SH_Default,
        SH_Mask,
        SH_Variant,
    };

    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QStyleHintReturn(int version = QStyleOption::StyleOptionVersion::Version, int type = QStyleHintReturn::HintReturnType::SH_Default);
    ~QStyleHintReturn();
    int version;
    int type;
};

class QStyleHintReturnMask : public QStyleHintReturn
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QStyleHintReturnMask();
    ~QStyleHintReturnMask();
    QRegion region;
};

class QStyleOptionToolBar : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    enum ToolBarPosition
    {
        Beginning,
        Middle,
        End,
        OnlyOne,
    };

    enum ToolBarFeature
    {
        None /PyName=None_/,
        Movable,
    };

    typedef QFlags<QStyleOptionToolBar::ToolBarFeature> ToolBarFeatures;
    QStyleOptionToolBar::ToolBarPosition positionOfLine;
    QStyleOptionToolBar::ToolBarPosition positionWithinLine;
    Qt::ToolBarArea toolBarArea;
    QStyleOptionToolBar::ToolBarFeatures features;
    int lineWidth;
    int midLineWidth;
    QStyleOptionToolBar();
    QStyleOptionToolBar(const QStyleOptionToolBar &other);
};

QFlags<QStyleOptionToolBar::ToolBarFeature> operator|(QStyleOptionToolBar::ToolBarFeature f1, QFlags<QStyleOptionToolBar::ToolBarFeature> f2);

class QStyleOptionGroupBox : public QStyleOptionComplex
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QStyleOptionFrame::FrameFeatures features;
    QString text;
    Qt::Alignment textAlignment;
    QColor textColor;
    int lineWidth;
    int midLineWidth;
    QStyleOptionGroupBox();
    QStyleOptionGroupBox(const QStyleOptionGroupBox &other);
};

class QStyleOptionSizeGrip : public QStyleOptionComplex
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    Qt::Corner corner;
    QStyleOptionSizeGrip();
    QStyleOptionSizeGrip(const QStyleOptionSizeGrip &other);
};

class QStyleOptionGraphicsItem : public QStyleOption
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QRectF exposedRect;
    static qreal levelOfDetailFromTransform(const QTransform &worldTransform);
    QStyleOptionGraphicsItem();
    QStyleOptionGraphicsItem(const QStyleOptionGraphicsItem &other);
};

class QStyleHintReturnVariant : public QStyleHintReturn
{
%TypeHeaderCode
#include <qstyleoption.h>
%End

public:
    enum StyleOptionType
    {
        Type,
    };

    enum StyleOptionVersion
    {
        Version,
    };

    QStyleHintReturnVariant();
    ~QStyleHintReturnVariant();
    QVariant variant;
};
