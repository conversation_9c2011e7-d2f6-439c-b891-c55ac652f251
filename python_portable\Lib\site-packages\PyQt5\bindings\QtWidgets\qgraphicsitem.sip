// qgraphicsitem.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsItem /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case 2:
        sipType = sipType_QGraphicsPathItem;
        break;
    
    case 3:
        sipType = sipType_QGraphicsRectItem;
        break;
    
    case 4:
        sipType = sipType_QGraphicsEllipseItem;
        break;
    
    case 5:
        sipType = sipType_QGraphicsPolygonItem;
        break;
    
    case 6:
        sipType = sipType_QGraphicsLineItem;
        break;
    
    case 7:
        sipType = sipType_QGraphicsPixmapItem;
        break;
    
    case 8:
        // Switch to the QObject convertor.
        *sipCppRet = static_cast<QGraphicsTextItem *>(sipCpp);
        sipType = sipType_QObject;
        break;
    
    case 9:
        sipType = sipType_QGraphicsSimpleTextItem;
        break;
    
    case 10:
        sipType = sipType_QGraphicsItemGroup;
        break;
    
    case 11:
        // Switch to the QObject convertor.
        *sipCppRet = static_cast<QGraphicsWidget *>(sipCpp);
        sipType = sipType_QObject;
        break;
    
    case 12:
        // Switch to the QObject convertor.
        *sipCppRet = static_cast<QGraphicsProxyWidget *>(sipCpp);
        sipType = sipType_QObject;
        break;
    
    default:
        sipType = 0;
    }
%End

public:
    enum CacheMode
    {
        NoCache,
        ItemCoordinateCache,
        DeviceCoordinateCache,
    };

    enum GraphicsItemChange
    {
        ItemPositionChange,
        ItemMatrixChange,
        ItemVisibleChange,
        ItemEnabledChange,
        ItemSelectedChange,
        ItemParentChange,
        ItemChildAddedChange,
        ItemChildRemovedChange,
        ItemTransformChange,
        ItemPositionHasChanged,
        ItemTransformHasChanged,
        ItemSceneChange,
        ItemVisibleHasChanged,
        ItemEnabledHasChanged,
        ItemSelectedHasChanged,
        ItemParentHasChanged,
        ItemSceneHasChanged,
        ItemCursorChange,
        ItemCursorHasChanged,
        ItemToolTipChange,
        ItemToolTipHasChanged,
        ItemFlagsChange,
        ItemFlagsHaveChanged,
        ItemZValueChange,
        ItemZValueHasChanged,
        ItemOpacityChange,
        ItemOpacityHasChanged,
        ItemScenePositionHasChanged,
        ItemRotationChange,
        ItemRotationHasChanged,
        ItemScaleChange,
        ItemScaleHasChanged,
        ItemTransformOriginPointChange,
        ItemTransformOriginPointHasChanged,
    };

    enum GraphicsItemFlag
    {
        ItemIsMovable,
        ItemIsSelectable,
        ItemIsFocusable,
        ItemClipsToShape,
        ItemClipsChildrenToShape,
        ItemIgnoresTransformations,
        ItemIgnoresParentOpacity,
        ItemDoesntPropagateOpacityToChildren,
        ItemStacksBehindParent,
        ItemUsesExtendedStyleOption,
        ItemHasNoContents,
        ItemSendsGeometryChanges,
        ItemAcceptsInputMethod,
        ItemNegativeZStacksBehindParent,
        ItemIsPanel,
        ItemSendsScenePositionChanges,
%If (Qt_5_4_0 -)
        ItemContainsChildrenInShape,
%End
    };

    typedef QFlags<QGraphicsItem::GraphicsItemFlag> GraphicsItemFlags;
    static const int Type;
    static const int UserType;
    explicit QGraphicsItem(QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsItem();
    QGraphicsScene *scene() const;
    QGraphicsItem *parentItem() const;
    QGraphicsItem *topLevelItem() const;
    void setParentItem(QGraphicsItem *parent /TransferThis/);
    QGraphicsItemGroup *group() const;
    void setGroup(QGraphicsItemGroup *group /KeepReference/);
    QGraphicsItem::GraphicsItemFlags flags() const;
    void setFlag(QGraphicsItem::GraphicsItemFlag flag, bool enabled = true);
    void setFlags(QGraphicsItem::GraphicsItemFlags flags);
    QString toolTip() const;
    void setToolTip(const QString &toolTip);
    QCursor cursor() const;
    void setCursor(const QCursor &cursor);
    bool hasCursor() const;
    void unsetCursor();
    bool isVisible() const;
    void setVisible(bool visible);
    void hide();
    void show();
    bool isEnabled() const;
    void setEnabled(bool enabled);
    bool isSelected() const;
    void setSelected(bool selected);
    bool acceptDrops() const;
    void setAcceptDrops(bool on);
    Qt::MouseButtons acceptedMouseButtons() const;
    void setAcceptedMouseButtons(Qt::MouseButtons buttons);
    bool hasFocus() const;
    void setFocus(Qt::FocusReason focusReason = Qt::OtherFocusReason);
    void clearFocus();
    QPointF pos() const;
    qreal x() const;
    qreal y() const;
    QPointF scenePos() const;
    void setPos(const QPointF &pos);
    void moveBy(qreal dx, qreal dy);
    void ensureVisible(const QRectF &rect = QRectF(), int xMargin = 50, int yMargin = 50);
    virtual void advance(int phase);
    qreal zValue() const;
    void setZValue(qreal z);
    virtual QRectF boundingRect() const = 0;
    QRectF childrenBoundingRect() const;
    QRectF sceneBoundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual bool collidesWithItem(const QGraphicsItem *other, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape) const;
    virtual bool collidesWithPath(const QPainterPath &path, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape) const;
    QList<QGraphicsItem *> collidingItems(Qt::ItemSelectionMode mode = Qt::IntersectsItemShape) const;
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0) = 0;
    void update(const QRectF &rect = QRectF());
    QPointF mapToItem(const QGraphicsItem *item, const QPointF &point) const;
    QPointF mapToParent(const QPointF &point) const;
    QPointF mapToScene(const QPointF &point) const;
    QPolygonF mapToItem(const QGraphicsItem *item, const QRectF &rect) const;
    QPolygonF mapToParent(const QRectF &rect) const;
    QPolygonF mapToScene(const QRectF &rect) const;
    QPolygonF mapToItem(const QGraphicsItem *item, const QPolygonF &polygon) const;
    QPolygonF mapToParent(const QPolygonF &polygon) const;
    QPolygonF mapToScene(const QPolygonF &polygon) const;
    QPainterPath mapToItem(const QGraphicsItem *item, const QPainterPath &path) const;
    QPainterPath mapToParent(const QPainterPath &path) const;
    QPainterPath mapToScene(const QPainterPath &path) const;
    QPointF mapFromItem(const QGraphicsItem *item, const QPointF &point) const;
    QPointF mapFromParent(const QPointF &point) const;
    QPointF mapFromScene(const QPointF &point) const;
    QPolygonF mapFromItem(const QGraphicsItem *item, const QRectF &rect) const;
    QPolygonF mapFromParent(const QRectF &rect) const;
    QPolygonF mapFromScene(const QRectF &rect) const;
    QPolygonF mapFromItem(const QGraphicsItem *item, const QPolygonF &polygon) const;
    QPolygonF mapFromParent(const QPolygonF &polygon) const;
    QPolygonF mapFromScene(const QPolygonF &polygon) const;
    QPainterPath mapFromItem(const QGraphicsItem *item, const QPainterPath &path) const;
    QPainterPath mapFromParent(const QPainterPath &path) const;
    QPainterPath mapFromScene(const QPainterPath &path) const;
    bool isAncestorOf(const QGraphicsItem *child) const;
    QVariant data(int key) const;
    void setData(int key, const QVariant &value);
    virtual int type() const;
    void installSceneEventFilter(QGraphicsItem *filterItem);
    void removeSceneEventFilter(QGraphicsItem *filterItem);

protected:
    virtual void contextMenuEvent(QGraphicsSceneContextMenuEvent *event);
    virtual void dragEnterEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dragLeaveEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dragMoveEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dropEvent(QGraphicsSceneDragDropEvent *event);
    virtual void focusInEvent(QFocusEvent *event);
    virtual void focusOutEvent(QFocusEvent *event);
    virtual void hoverEnterEvent(QGraphicsSceneHoverEvent *event);
    virtual void hoverLeaveEvent(QGraphicsSceneHoverEvent *event);
    virtual void hoverMoveEvent(QGraphicsSceneHoverEvent *event);
    virtual void inputMethodEvent(QInputMethodEvent *event);
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery query) const;
    virtual QVariant itemChange(QGraphicsItem::GraphicsItemChange change, const QVariant &value);
    virtual void keyPressEvent(QKeyEvent *event);
    virtual void keyReleaseEvent(QKeyEvent *event);
    virtual void mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event);
    virtual void mouseMoveEvent(QGraphicsSceneMouseEvent *event);
    virtual void mousePressEvent(QGraphicsSceneMouseEvent *event);
    virtual void mouseReleaseEvent(QGraphicsSceneMouseEvent *event);
    void prepareGeometryChange();
    virtual bool sceneEvent(QEvent *event);
    virtual bool sceneEventFilter(QGraphicsItem *watched, QEvent *event);
    virtual void wheelEvent(QGraphicsSceneWheelEvent *event);

public:
    void setPos(qreal ax, qreal ay);
    void ensureVisible(qreal x, qreal y, qreal w, qreal h, int xMargin = 50, int yMargin = 50);
    void update(qreal ax, qreal ay, qreal width, qreal height);
    QPointF mapToItem(const QGraphicsItem *item, qreal ax, qreal ay) const;
    QPointF mapToParent(qreal ax, qreal ay) const;
    QPointF mapToScene(qreal ax, qreal ay) const;
    QPointF mapFromItem(const QGraphicsItem *item, qreal ax, qreal ay) const;
    QPointF mapFromParent(qreal ax, qreal ay) const;
    QPointF mapFromScene(qreal ax, qreal ay) const;
    QTransform transform() const;
    QTransform sceneTransform() const;
    QTransform deviceTransform(const QTransform &viewportTransform) const;
    void setTransform(const QTransform &matrix, bool combine = false);
    void resetTransform();
    bool isObscured(const QRectF &rect = QRectF()) const;
    bool isObscured(qreal ax, qreal ay, qreal w, qreal h) const;
    QPolygonF mapToItem(const QGraphicsItem *item, qreal ax, qreal ay, qreal w, qreal h) const;
    QPolygonF mapToParent(qreal ax, qreal ay, qreal w, qreal h) const;
    QPolygonF mapToScene(qreal ax, qreal ay, qreal w, qreal h) const;
    QPolygonF mapFromItem(const QGraphicsItem *item, qreal ax, qreal ay, qreal w, qreal h) const;
    QPolygonF mapFromParent(qreal ax, qreal ay, qreal w, qreal h) const;
    QPolygonF mapFromScene(qreal ax, qreal ay, qreal w, qreal h) const;
    QGraphicsWidget *parentWidget() const;
    QGraphicsWidget *topLevelWidget() const;
    QGraphicsWidget *window() const;
    QList<QGraphicsItem *> childItems() const;
    bool isWidget() const;
    bool isWindow() const;
    QGraphicsItem::CacheMode cacheMode() const;
    void setCacheMode(QGraphicsItem::CacheMode mode, const QSize &logicalCacheSize = QSize());
    bool isVisibleTo(const QGraphicsItem *parent) const;
    bool acceptHoverEvents() const;
    void setAcceptHoverEvents(bool enabled);
    void grabMouse();
    void ungrabMouse();
    void grabKeyboard();
    void ungrabKeyboard();
    QRegion boundingRegion(const QTransform &itemToDeviceTransform) const;
    qreal boundingRegionGranularity() const;
    void setBoundingRegionGranularity(qreal granularity);
    void scroll(qreal dx, qreal dy, const QRectF &rect = QRectF());
    QGraphicsItem *commonAncestorItem(const QGraphicsItem *other) const;
    bool isUnderMouse() const;
    qreal opacity() const;
    qreal effectiveOpacity() const;
    void setOpacity(qreal opacity);
    QTransform itemTransform(const QGraphicsItem *other, bool *ok = 0) const;
    bool isClipped() const;
    QPainterPath clipPath() const;
    QRectF mapRectToItem(const QGraphicsItem *item, const QRectF &rect) const;
    QRectF mapRectToParent(const QRectF &rect) const;
    QRectF mapRectToScene(const QRectF &rect) const;
    QRectF mapRectFromItem(const QGraphicsItem *item, const QRectF &rect) const;
    QRectF mapRectFromParent(const QRectF &rect) const;
    QRectF mapRectFromScene(const QRectF &rect) const;
    QRectF mapRectToItem(const QGraphicsItem *item, qreal ax, qreal ay, qreal w, qreal h) const;
    QRectF mapRectToParent(qreal ax, qreal ay, qreal w, qreal h) const;
    QRectF mapRectToScene(qreal ax, qreal ay, qreal w, qreal h) const;
    QRectF mapRectFromItem(const QGraphicsItem *item, qreal ax, qreal ay, qreal w, qreal h) const;
    QRectF mapRectFromParent(qreal ax, qreal ay, qreal w, qreal h) const;
    QRectF mapRectFromScene(qreal ax, qreal ay, qreal w, qreal h) const;

    enum PanelModality
    {
        NonModal,
        PanelModal,
        SceneModal,
    };

    QGraphicsObject *parentObject() const;
    QGraphicsItem *panel() const;
    bool isPanel() const;
    QGraphicsObject *toGraphicsObject();
    QGraphicsItem::PanelModality panelModality() const;
    void setPanelModality(QGraphicsItem::PanelModality panelModality);
    bool isBlockedByModalPanel(QGraphicsItem **blockingPanel /Out/ = 0) const;
    QGraphicsEffect *graphicsEffect() const;
    void setGraphicsEffect(QGraphicsEffect *effect /Transfer/);
    bool acceptTouchEvents() const;
    void setAcceptTouchEvents(bool enabled);
    bool filtersChildEvents() const;
    void setFiltersChildEvents(bool enabled);
    bool isActive() const;
    void setActive(bool active);
    QGraphicsItem *focusProxy() const;
    void setFocusProxy(QGraphicsItem *item /KeepReference/);
    QGraphicsItem *focusItem() const;
    void setX(qreal x);
    void setY(qreal y);
    void setRotation(qreal angle);
    qreal rotation() const;
    void setScale(qreal scale);
    qreal scale() const;
    QList<QGraphicsTransform *> transformations() const;
    void setTransformations(const QList<QGraphicsTransform *> &transformations /KeepReference/);
    QPointF transformOriginPoint() const;
    void setTransformOriginPoint(const QPointF &origin);
    void setTransformOriginPoint(qreal ax, qreal ay);
    void stackBefore(const QGraphicsItem *sibling);
    Qt::InputMethodHints inputMethodHints() const;
    void setInputMethodHints(Qt::InputMethodHints hints);

protected:
    void updateMicroFocus();

private:
    QGraphicsItem(const QGraphicsItem &);
};

QFlags<QGraphicsItem::GraphicsItemFlag> operator|(QGraphicsItem::GraphicsItemFlag f1, QFlags<QGraphicsItem::GraphicsItemFlag> f2);

class QAbstractGraphicsShapeItem : public QGraphicsItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QAbstractGraphicsShapeItem(QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QAbstractGraphicsShapeItem();
    QPen pen() const;
    void setPen(const QPen &pen);
    QBrush brush() const;
    void setBrush(const QBrush &brush);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
};

class QGraphicsPathItem : public QAbstractGraphicsShapeItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsPathItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsPathItem(const QPainterPath &path, QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsPathItem();
    QPainterPath path() const;
    void setPath(const QPainterPath &path);
    virtual QRectF boundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
};

class QGraphicsRectItem : public QAbstractGraphicsShapeItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsRectItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsRectItem(const QRectF &rect, QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsRectItem(qreal x, qreal y, qreal w, qreal h, QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsRectItem();
    QRectF rect() const;
    void setRect(const QRectF &rect);
    void setRect(qreal ax, qreal ay, qreal w, qreal h);
    virtual QRectF boundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
};

class QGraphicsEllipseItem : public QAbstractGraphicsShapeItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsEllipseItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsEllipseItem(const QRectF &rect, QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsEllipseItem(qreal x, qreal y, qreal w, qreal h, QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsEllipseItem();
    QRectF rect() const;
    void setRect(const QRectF &rect);
    void setRect(qreal ax, qreal ay, qreal w, qreal h);
    int startAngle() const;
    void setStartAngle(int angle);
    int spanAngle() const;
    void setSpanAngle(int angle);
    virtual QRectF boundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
};

class QGraphicsPolygonItem : public QAbstractGraphicsShapeItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsPolygonItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsPolygonItem(const QPolygonF &polygon, QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsPolygonItem();
    QPolygonF polygon() const;
    void setPolygon(const QPolygonF &polygon);
    Qt::FillRule fillRule() const;
    void setFillRule(Qt::FillRule rule);
    virtual QRectF boundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
};

class QGraphicsLineItem : public QGraphicsItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsLineItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsLineItem(const QLineF &line, QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsLineItem(qreal x1, qreal y1, qreal x2, qreal y2, QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsLineItem();
    QPen pen() const;
    void setPen(const QPen &pen);
    QLineF line() const;
    void setLine(const QLineF &line);
    void setLine(qreal x1, qreal y1, qreal x2, qreal y2);
    virtual QRectF boundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
};

class QGraphicsPixmapItem : public QGraphicsItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    enum ShapeMode
    {
        MaskShape,
        BoundingRectShape,
        HeuristicMaskShape,
    };

    explicit QGraphicsPixmapItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsPixmapItem(const QPixmap &pixmap, QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsPixmapItem();
    QPixmap pixmap() const;
    void setPixmap(const QPixmap &pixmap);
    Qt::TransformationMode transformationMode() const;
    void setTransformationMode(Qt::TransformationMode mode);
    QPointF offset() const;
    void setOffset(const QPointF &offset);
    void setOffset(qreal ax, qreal ay);
    virtual QRectF boundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
    QGraphicsPixmapItem::ShapeMode shapeMode() const;
    void setShapeMode(QGraphicsPixmapItem::ShapeMode mode);
};

class QGraphicsSimpleTextItem : public QAbstractGraphicsShapeItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsSimpleTextItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsSimpleTextItem(const QString &text, QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsSimpleTextItem();
    void setText(const QString &text);
    QString text() const;
    void setFont(const QFont &font);
    QFont font() const;
    virtual QRectF boundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
};

class QGraphicsItemGroup : public QGraphicsItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsItemGroup(QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsItemGroup();
    void addToGroup(QGraphicsItem *item /Transfer/);
    void removeFromGroup(QGraphicsItem *item /GetWrapper/);
%MethodCode
        sipCpp->removeFromGroup(a0);
        
        // The item will be passed to the group's parent if there is one.  If not,
        // transfer ownership back to Python.
        if (sipCpp->parentItem())
            sipTransferTo(a0Wrapper, sipGetPyObject(sipCpp->parentItem(), sipType_QGraphicsItem));
        else
            sipTransferBack(a0Wrapper);
%End

    virtual QRectF boundingRect() const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
};

class QGraphicsObject : public QObject, public QGraphicsItem
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsObject(QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsObject();
    void grabGesture(Qt::GestureType type, Qt::GestureFlags flags = Qt::GestureFlags());
    void ungrabGesture(Qt::GestureType type);

signals:
    void parentChanged();
    void opacityChanged();
    void visibleChanged();
    void enabledChanged();
    void xChanged();
    void yChanged();
    void zChanged();
    void rotationChanged();
    void scaleChanged();

protected slots:
    void updateMicroFocus();

protected:
    virtual bool event(QEvent *ev);
};

class QGraphicsTextItem : public QGraphicsObject
{
%TypeHeaderCode
#include <qgraphicsitem.h>
%End

public:
    explicit QGraphicsTextItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsTextItem(const QString &text, QGraphicsItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsTextItem();
    QString toHtml() const;
    void setHtml(const QString &html);
    QString toPlainText() const;
    void setPlainText(const QString &text);
    QFont font() const;
    void setFont(const QFont &font);
    void setDefaultTextColor(const QColor &c);
    QColor defaultTextColor() const;
    virtual QRectF boundingRect() const;
    virtual QPainterPath shape() const;
    virtual bool contains(const QPointF &point) const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget);
    virtual bool isObscuredBy(const QGraphicsItem *item) const;
    virtual QPainterPath opaqueArea() const;
    virtual int type() const;
    void setTextWidth(qreal width);
    qreal textWidth() const;
    void adjustSize();
    void setDocument(QTextDocument *document /KeepReference/);
    QTextDocument *document() const;
    void setTextInteractionFlags(Qt::TextInteractionFlags flags);
    Qt::TextInteractionFlags textInteractionFlags() const;
    void setTabChangesFocus(bool b);
    bool tabChangesFocus() const;
    void setOpenExternalLinks(bool open);
    bool openExternalLinks() const;
    void setTextCursor(const QTextCursor &cursor);
    QTextCursor textCursor() const;

signals:
    void linkActivated(const QString &);
    void linkHovered(const QString &);

protected:
    virtual bool sceneEvent(QEvent *event);
    virtual void mousePressEvent(QGraphicsSceneMouseEvent *event);
    virtual void mouseMoveEvent(QGraphicsSceneMouseEvent *event);
    virtual void mouseReleaseEvent(QGraphicsSceneMouseEvent *event);
    virtual void mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event);
    virtual void contextMenuEvent(QGraphicsSceneContextMenuEvent *event);
    virtual void keyPressEvent(QKeyEvent *event);
    virtual void keyReleaseEvent(QKeyEvent *event);
    virtual void focusInEvent(QFocusEvent *event);
    virtual void focusOutEvent(QFocusEvent *event);
    virtual void dragEnterEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dragLeaveEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dragMoveEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dropEvent(QGraphicsSceneDragDropEvent *event);
    virtual void inputMethodEvent(QInputMethodEvent *event);
    virtual void hoverEnterEvent(QGraphicsSceneHoverEvent *event);
    virtual void hoverMoveEvent(QGraphicsSceneHoverEvent *event);
    virtual void hoverLeaveEvent(QGraphicsSceneHoverEvent *event);
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery query) const;
};

%ModuleCode
// These are needed by the %ConvertToSubClassCode.
#include <qgraphicsproxywidget.h>
#include <qgraphicswidget.h>
%End
