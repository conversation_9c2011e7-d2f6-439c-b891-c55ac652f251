#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建示例数据
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager

def create_sample_data():
    """创建示例数据"""
    print("正在创建示例数据...")
    
    # 创建数据库管理器
    db = DatabaseManager("database.db")
    
    try:
        # 添加示例用户
        users = [
            ("张三", "<EMAIL>", 25, "13800138000"),
            ("李四", "<EMAIL>", 30, "13900139000"),
            ("王五", "<EMAIL>", 28, "13700137000"),
            ("赵六", "<EMAIL>", 35, "13600136000"),
            ("孙七", "<EMAIL>", 22, "13500135000"),
        ]
        
        print("添加示例用户...")
        for name, email, age, phone in users:
            if db.add_user(name, email, age, phone):
                print(f"  ✓ 用户 {name} 添加成功")
            else:
                print(f"  ✗ 用户 {name} 添加失败")
        
        # 添加示例产品
        products = [
            ("笔记本电脑", 5999.99, "电子产品", 10),
            ("无线鼠标", 99.99, "电子产品", 50),
            ("机械键盘", 299.99, "电子产品", 30),
            ("显示器", 1299.99, "电子产品", 15),
            ("耳机", 199.99, "电子产品", 25),
            ("手机", 2999.99, "电子产品", 20),
            ("平板电脑", 1999.99, "电子产品", 12),
            ("充电器", 49.99, "电子产品", 100),
        ]
        
        print("添加示例产品...")
        for name, price, category, stock in products:
            if db.add_product(name, price, category, stock):
                print(f"  ✓ 产品 {name} 添加成功")
            else:
                print(f"  ✗ 产品 {name} 添加失败")
        
        print("\n✅ 示例数据创建完成！")
        print("现在可以运行 main.py 来查看和管理数据。")
        
    except Exception as e:
        print(f"❌ 创建示例数据时出现错误: {e}")
    
    finally:
        # 关闭数据库连接
        db.close()

if __name__ == "__main__":
    create_sample_data()
