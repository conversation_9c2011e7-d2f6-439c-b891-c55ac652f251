// qundostack.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QUndoCommand /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qundostack.h>
%End

public:
    explicit QUndoCommand(QUndoCommand *parent /TransferThis/ = 0);
    QUndoCommand(const QString &text, QUndoCommand *parent /TransferThis/ = 0);
    virtual ~QUndoCommand();
    virtual int id() const;
    virtual bool mergeWith(const QUndoCommand *other);
    virtual void redo();
    void setText(const QString &text);
    QString text() const;
    virtual void undo();
    int childCount() const;
    const QUndoCommand *child(int index) const;
    QString actionText() const;
%If (Qt_5_9_0 -)
    bool isObsolete() const;
%End
%If (Qt_5_9_0 -)
    void setObsolete(bool obsolete);
%End

private:
    QUndoCommand(const QUndoCommand &);
};

class QUndoStack : public QObject
{
%TypeHeaderCode
#include <qundostack.h>
%End

public:
    explicit QUndoStack(QObject *parent /TransferThis/ = 0);
    virtual ~QUndoStack();
    void clear();
    void push(QUndoCommand *cmd /Transfer/);
    bool canUndo() const;
    bool canRedo() const;
    QString undoText() const;
    QString redoText() const;
    int count() const /__len__/;
    int index() const;
    QString text(int idx) const;
    QAction *createUndoAction(QObject *parent /TransferThis/, const QString &prefix = QString()) const /Factory/;
    QAction *createRedoAction(QObject *parent /TransferThis/, const QString &prefix = QString()) const /Factory/;
    bool isActive() const;
    bool isClean() const;
    int cleanIndex() const;
    void beginMacro(const QString &text);
    void endMacro();

public slots:
    void redo();
    void setActive(bool active = true);
    void setClean();
    void setIndex(int idx);
    void undo();
%If (Qt_5_8_0 -)
    void resetClean();
%End

signals:
    void canRedoChanged(bool canRedo);
    void canUndoChanged(bool canUndo);
    void cleanChanged(bool clean);
    void indexChanged(int idx);
    void redoTextChanged(const QString &redoText);
    void undoTextChanged(const QString &undoText);

public:
    void setUndoLimit(int limit);
    int undoLimit() const;
    const QUndoCommand *command(int index) const;
};
