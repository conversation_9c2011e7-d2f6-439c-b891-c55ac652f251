// qaction.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAction : public QObject
{
%TypeHeaderCode
#include <qaction.h>
%End

public:
%If (Qt_5_7_0 -)
    explicit QAction(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_7_0)
    explicit QAction(QObject *parent /TransferThis/);
%End
%If (Qt_5_7_0 -)
    QAction(const QString &text, QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_7_0)
    QAction(const QString &text, QObject *parent /TransferThis/);
%End
%If (Qt_5_7_0 -)
    QAction(const QIcon &icon, const QString &text, QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_7_0)
    QAction(const QIcon &icon, const QString &text, QObject *parent /TransferThis/);
%End
    virtual ~QAction();
    void setActionGroup(QActionGroup *group /KeepReference/);
    QActionGroup *actionGroup() const;
    void setIcon(const QIcon &icon);
    QIcon icon() const;
    void setText(const QString &text);
    QString text() const;
    void setIconText(const QString &text);
    QString iconText() const;
    void setToolTip(const QString &tip);
    QString toolTip() const;
    void setStatusTip(const QString &statusTip);
    QString statusTip() const;
    void setWhatsThis(const QString &what);
    QString whatsThis() const;
    QMenu *menu() const;
    void setMenu(QMenu *menu /KeepReference/);
    void setSeparator(bool b);
    bool isSeparator() const;
    void setShortcut(const QKeySequence &shortcut);
    QKeySequence shortcut() const;
    void setShortcutContext(Qt::ShortcutContext context);
    Qt::ShortcutContext shortcutContext() const;
    void setFont(const QFont &font);
    QFont font() const;
    void setCheckable(bool);
    bool isCheckable() const;
    QVariant data() const;
    void setData(const QVariant &var);
    bool isChecked() const;
    bool isEnabled() const;
    bool isVisible() const;

    enum ActionEvent
    {
        Trigger,
        Hover,
    };

    void activate(QAction::ActionEvent event);
    bool showStatusText(QWidget *widget = 0);
    QWidget *parentWidget() const;

protected:
    virtual bool event(QEvent *);

public slots:
    void trigger();
    void hover();
    void setChecked(bool);
    void toggle();
    void setEnabled(bool);
    void setDisabled(bool b);
    void setVisible(bool);

signals:
    void changed();
    void triggered(bool checked = false);
    void hovered();
    void toggled(bool);

public:
    enum MenuRole
    {
        NoRole,
        TextHeuristicRole,
        ApplicationSpecificRole,
        AboutQtRole,
        AboutRole,
        PreferencesRole,
        QuitRole,
    };

    void setShortcuts(const QList<QKeySequence> &shortcuts);
    void setShortcuts(QKeySequence::StandardKey);
    QList<QKeySequence> shortcuts() const;
    void setAutoRepeat(bool);
    bool autoRepeat() const;
    void setMenuRole(QAction::MenuRole menuRole);
    QAction::MenuRole menuRole() const;
    QList<QWidget *> associatedWidgets() const;
    QList<QGraphicsWidget *> associatedGraphicsWidgets() const;
    void setIconVisibleInMenu(bool visible);
    bool isIconVisibleInMenu() const;

    enum Priority
    {
        LowPriority,
        NormalPriority,
        HighPriority,
    };

    void setPriority(QAction::Priority priority);
    QAction::Priority priority() const;
%If (Qt_5_10_0 -)
    void setShortcutVisibleInContextMenu(bool show);
%End
%If (Qt_5_10_0 -)
    bool isShortcutVisibleInContextMenu() const;
%End
};
