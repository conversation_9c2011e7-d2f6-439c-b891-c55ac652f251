// qndefrecord.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QNdefRecord
{
%TypeHeaderCode
#include <qndefrecord.h>
%End

%ConvertToSubClassCode
    QByteArray ndef_type = sipCpp->type();
    
    switch (sipCpp->typeNameFormat())
    {
    case QNdefRecord::NfcRtd:
        if (ndef_type == "Sp")
            sipType = sipType_QNdefNfcSmartPosterRecord;
        else if (ndef_type == "T")
            sipType = sipType_QNdefNfcTextRecord;
        else if (ndef_type == "U")
            sipType = sipType_QNdefNfcUriRecord;
        else
            sipType = 0;
        
        break;
    
    case QNdefRecord::Mime:
        if (ndef_type == "")
            sipType = sipType_QNdefNfcIconRecord;
        else
            sipType = 0;
        
        break;
        
    default:
        sipType = 0;
    }
%End

public:
    enum TypeNameFormat
    {
        Empty,
        NfcRtd,
        Mime,
        Uri,
        ExternalRtd,
        Unknown,
    };

    QNdefRecord();
    QNdefRecord(const QNdefRecord &other);
    ~QNdefRecord();
    void setTypeNameFormat(QNdefRecord::TypeNameFormat typeNameFormat);
    QNdefRecord::TypeNameFormat typeNameFormat() const;
    void setType(const QByteArray &type);
    QByteArray type() const;
    void setId(const QByteArray &id);
    QByteArray id() const;
    void setPayload(const QByteArray &payload);
    QByteArray payload() const;
    bool isEmpty() const;
    bool operator==(const QNdefRecord &other) const;
    bool operator!=(const QNdefRecord &other) const;
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
