// qquickrendercontrol.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)

class QQuickRenderControl : public QObject
{
%TypeHeaderCode
#include <qquickrendercontrol.h>
%End

public:
%If (Qt_5_6_1 -)
    explicit QQuickRenderControl(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QQuickRenderControl(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QQuickRenderControl();
%If (PyQt_OpenGL)
    void initialize(QOpenGLContext *gl);
%End
    void invalidate();
    void polishItems();
    void render();
    bool sync();
    QImage grab();
    static QWindow *renderWindowFor(QQuickWindow *win, QPoint *offset = 0);
    virtual QWindow *renderWindow(QPoint *offset);
%If (Qt_5_5_0 -)
    void prepareThread(QThread *targetThread);
%End

signals:
    void renderRequested();
    void sceneChanged();
};

%End
