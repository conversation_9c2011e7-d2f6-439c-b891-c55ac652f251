// qquickpainteditem.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickPaintedItem : public QQuickItem /ExportDerived/
{
%TypeHeaderCode
#include <qquickpainteditem.h>
%End

public:
%If (Qt_5_6_1 -)
    explicit QQuickPaintedItem(QQuickItem *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QQuickPaintedItem(QQuickItem *parent /TransferThis/ = 0);
%End
    virtual ~QQuickPaintedItem();

    enum RenderTarget
    {
        Image,
        FramebufferObject,
        InvertedYFramebufferObject,
    };

    enum PerformanceHint
    {
        FastFBOResizing,
    };

    typedef QFlags<QQuickPaintedItem::PerformanceHint> PerformanceHints;
    void update(const QRect &rect = QRect());
    bool opaquePainting() const;
    void setOpaquePainting(bool opaque);
    bool antialiasing() const;
    void setAntialiasing(bool enable);
    bool mipmap() const;
    void setMipmap(bool enable);
    QQuickPaintedItem::PerformanceHints performanceHints() const;
    void setPerformanceHint(QQuickPaintedItem::PerformanceHint hint, bool enabled = true);
    void setPerformanceHints(QQuickPaintedItem::PerformanceHints hints);
    QRectF contentsBoundingRect() const;
    QSize contentsSize() const;
    void setContentsSize(const QSize &);
    void resetContentsSize();
    qreal contentsScale() const;
    void setContentsScale(qreal);
    QColor fillColor() const;
    void setFillColor(const QColor &);
    QQuickPaintedItem::RenderTarget renderTarget() const;
    void setRenderTarget(QQuickPaintedItem::RenderTarget target);
    virtual void paint(QPainter *painter) = 0;

signals:
    void fillColorChanged();
    void contentsSizeChanged();
    void contentsScaleChanged();
    void renderTargetChanged();

protected:
    virtual QSGNode *updatePaintNode(QSGNode *, QQuickItem::UpdatePaintNodeData *);

public:
%If (Qt_5_4_0 -)
    virtual bool isTextureProvider() const;
%End
%If (Qt_5_4_0 -)
    virtual QSGTextureProvider *textureProvider() const;
%End

protected:
%If (Qt_5_4_0 -)
    virtual void releaseResources();
%End
%If (Qt_5_6_1 -)
    virtual void itemChange(QQuickItem::ItemChange, const QQuickItem::ItemChangeData &);
%End

public:
%If (Qt_5_6_0 -)
    QSize textureSize() const;
%End
%If (Qt_5_6_0 -)
    void setTextureSize(const QSize &size);
%End

signals:
%If (Qt_5_6_0 -)
    void textureSizeChanged();
%End
};

QFlags<QQuickPaintedItem::PerformanceHint> operator|(QQuickPaintedItem::PerformanceHint f1, QFlags<QQuickPaintedItem::PerformanceHint> f2);
